export enum ErrorCodeEnum {
  // 短信验证码相关错误 1000-1099
  SMS_REPEATED = 1001,
  SMS_CODE_NOT_EXIST = 1002,

  // 小程序相关错误 1100-1199
  MINIAPP_LOGIN_FAILED = 1101,

  // 管理员相关错误 2000-2099
  ADMIN_USER_NOT_FOUND = 2001,

  // 用户相关错误 3000-3099
  USER_NOT_FOUND = 3001,
  PHONE_ALREADY_EXISTS = 3002,
  EMAIL_ALREADY_EXISTS = 3003,

  // 系统级错误 4000-4099
  LOGIN_FAILED = 4001,
  VALIDATE_FAILED = 4002,
  NO_PERMISSION = 4003,
  FORBIDDEN = 4004,
  SERVER_BUSY = 4005,

  // 剧集相关错误 5000-5099
  DRAMA_NOT_FOUND = 5001,
  DRAMA_CREATE_FAILED = 5002,
  DRAMA_UPDATE_FAILED = 5003,
  DRAMA_NO_EPISODES = 5004,
  DRAMA_NOT_CREATED_IN_DOUYIN = 5005,
  DRAMA_NO_VERSION = 5006,
  DRAMA_NOT_ONLINE = 5007,
  DRAMA_IN_REVIEW = 5008,
  DRAMA_REVIEW_IN_PROGRESS = 5404,
  DRAMA_REVIEW_FAILED = 5405,

  // 分集相关错误 5100-5199
  EPISODE_NOT_FOUND = 5101,
  EPISODE_DRAMA_NOT_FOUND = 5102,
  EPISODE_ORDER_INVALID = 5103,
  EPISODE_NUMBER_DUPLICATE = 5104,
  EPISODE_REQUIRED_FIELDS_MISSING = 5105,
  EPISODE_NUMBER_INVALID = 5106,
  EPISODE_PRICE_INVALID = 5107,
  EPISODE_CREATE_FAILED = 5108,
  EPISODE_SEQ_DUPLICATE = 5109,
  EPISODE_SEQ_EXCEED_LIMIT = 5110,
  EPISODE_MISSING_DOUYIN_RESOURCES = 5111,

  // 订单相关错误 5200-5299
  ORDER_NOT_FOUND = 5201,
  ORDER_STATUS_INVALID = 5202,
  ORDER_AMOUNT_INVALID = 5203,
  ORDER_EXPIRED = 5204,
  ORDER_ALREADY_PAID = 5205,
  ORDER_CANNOT_REFUND = 5206,
  ORDER_REFUND_EXISTED = 5207,
  PAYMENT_FAILED = 5208,
  REFUND_FAILED = 5209,

  // 类目相关错误 5300-5399
  GENRES_CREATE_FAILED = 5301,
  GENRES_NAME_EXISTED = 5302,

  // 抖音相关错误 5400-5499
  DOUYIN_CLIENT_TOKEN_FAILED = 5401,
  DOUYIN_API_ERROR = 5402,
  DOUYIN_UPLOAD_FAILED = 5403,

  // 评论相关错误 5500-5599
  COMMENT_NOT_FOUND = 5501,
  COMMENT_FEATURE_FAILED = 5502,

  // 回调错误 5600-5699
  DY_CALLBACK_VERSION_NOT_SUPPORTED = 5601,
}

export const ErrorCode = Object.freeze<Record<ErrorCodeEnum, [string, number]>>({
  [ErrorCodeEnum.SMS_REPEATED]: ['验证码5分钟内有效', 200],
  [ErrorCodeEnum.SMS_CODE_NOT_EXIST]: ['验证码不存在', 200],
  [ErrorCodeEnum.MINIAPP_LOGIN_FAILED]: ['小程序登录失败', 200],
  [ErrorCodeEnum.ADMIN_USER_NOT_FOUND]: ['后台用户不存在', 200],
  [ErrorCodeEnum.USER_NOT_FOUND]: ['用户不存在', 200],
  [ErrorCodeEnum.PHONE_ALREADY_EXISTS]: ['手机号已被使用', 200],
  [ErrorCodeEnum.EMAIL_ALREADY_EXISTS]: ['邮箱已被使用', 200],
  [ErrorCodeEnum.LOGIN_FAILED]: ['用户认证失败', 200],
  [ErrorCodeEnum.VALIDATE_FAILED]: ['参数校验失败', 200],
  [ErrorCodeEnum.NO_PERMISSION]: ['没有权限', 200],
  [ErrorCodeEnum.FORBIDDEN]: ['没有权限', 200],
  [ErrorCodeEnum.SERVER_BUSY]: ['服务器繁忙', 200],

  // 剧集相关错误
  [ErrorCodeEnum.DRAMA_NOT_FOUND]: ['短剧不存在', 200],
  [ErrorCodeEnum.DRAMA_CREATE_FAILED]: ['短剧创建失败', 200],
  [ErrorCodeEnum.DRAMA_UPDATE_FAILED]: ['短剧修改失败', 200],
  [ErrorCodeEnum.DRAMA_NO_EPISODES]: ['短剧下没有分集，无法送审', 200],
  [ErrorCodeEnum.DRAMA_NOT_CREATED_IN_DOUYIN]: ['短剧未在抖音创建', 200],
  [ErrorCodeEnum.DRAMA_NO_VERSION]: ['短剧没有可用版本', 200],
  [ErrorCodeEnum.DRAMA_NOT_ONLINE]: ['短剧未上线', 200],
  [ErrorCodeEnum.DRAMA_IN_REVIEW]: ['短剧正在审核中，无法添加分集', 200],

  // 分集相关错误
  [ErrorCodeEnum.EPISODE_NOT_FOUND]: ['剧集不存在', 200],
  [ErrorCodeEnum.EPISODE_DRAMA_NOT_FOUND]: ['所属短剧不存在', 200],
  [ErrorCodeEnum.EPISODE_ORDER_INVALID]: ['集数序号无效', 200],
  [ErrorCodeEnum.EPISODE_NUMBER_DUPLICATE]: ['分集集数重复', 200],
  [ErrorCodeEnum.EPISODE_REQUIRED_FIELDS_MISSING]: ['分集必填字段缺失', 200],
  [ErrorCodeEnum.EPISODE_NUMBER_INVALID]: ['分集集数无效', 200],
  [ErrorCodeEnum.EPISODE_PRICE_INVALID]: ['分集价格格式无效', 200],
  [ErrorCodeEnum.EPISODE_CREATE_FAILED]: ['分集创建失败', 200],
  [ErrorCodeEnum.EPISODE_SEQ_DUPLICATE]: ['分集集数重复', 200],
  [ErrorCodeEnum.EPISODE_SEQ_EXCEED_LIMIT]: ['分集集数超过短剧总集数', 200],
  [ErrorCodeEnum.EPISODE_MISSING_DOUYIN_RESOURCES]: ['存在分集缺少抖音图片或视频资源，无法编辑短剧', 200],

  // 订单相关错误
  [ErrorCodeEnum.ORDER_NOT_FOUND]: ['订单不存在', 200],
  [ErrorCodeEnum.ORDER_STATUS_INVALID]: ['订单状态无效', 200],
  [ErrorCodeEnum.ORDER_AMOUNT_INVALID]: ['订单金额无效', 200],
  [ErrorCodeEnum.ORDER_EXPIRED]: ['订单已过期', 200],
  [ErrorCodeEnum.ORDER_ALREADY_PAID]: ['订单已支付', 200],
  [ErrorCodeEnum.ORDER_CANNOT_REFUND]: ['订单不能退款', 200],
  [ErrorCodeEnum.ORDER_REFUND_EXISTED]: ['订单已申请退款', 200],
  [ErrorCodeEnum.PAYMENT_FAILED]: ['支付失败', 200],
  [ErrorCodeEnum.REFUND_FAILED]: ['退款失败', 200],

  // 类目相关错误
  [ErrorCodeEnum.GENRES_CREATE_FAILED]: ['类目创建失败', 200],
  [ErrorCodeEnum.GENRES_NAME_EXISTED]: ['类目名称已存在', 200],

  // 抖音相关错误
  [ErrorCodeEnum.DOUYIN_CLIENT_TOKEN_FAILED]: ['抖音客户端token获取失败', 200],
  [ErrorCodeEnum.DOUYIN_API_ERROR]: ['抖音API调用失败', 200],
  [ErrorCodeEnum.DOUYIN_UPLOAD_FAILED]: ['短剧资源上传失败', 200],
  [ErrorCodeEnum.DRAMA_REVIEW_IN_PROGRESS]: ['短剧正在审核中，不能重复送审', 200],
  [ErrorCodeEnum.DRAMA_REVIEW_FAILED]: ['短剧送审失败', 200],

  // 评论相关错误
  [ErrorCodeEnum.COMMENT_NOT_FOUND]: ['评论不存在', 200],
  [ErrorCodeEnum.COMMENT_FEATURE_FAILED]: ['精选评论操作失败', 200],

  // 回调错误
  [ErrorCodeEnum.DY_CALLBACK_VERSION_NOT_SUPPORTED]: ['抖音回调版本不支持', 200],
})
